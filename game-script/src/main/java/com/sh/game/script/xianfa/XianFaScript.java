package com.sh.game.script.xianfa;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.system.xianfa.*;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.sys.XianFaData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnServerStartUpScript;
import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.XianFaProtos;
import com.sh.game.script.activity.abc.AbstractRankingActivity;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.query.QueryManager;
import com.sh.game.system.render.entity.RenderData;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.game.system.role.script.IRoleOnSecondScript;
import com.sh.game.system.secretary.SecretaryManager;
import com.sh.game.system.secretary.entity.SecretaryData;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.xianfa.entity.*;
import com.sh.game.system.xianfa.script.IXianFaScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-03-11
 */
@Slf4j
@Script
public class XianFaScript extends AbstractRankingActivity implements IXianFaScript, IEventOnRoleMidnightScript, IEventScheduleUpdateOnMinuteScript, IRoleOnSecondScript, IEventOnServerStartUpScript {

    @Override
    public int getType() {
        return ActivityConst.XIAN_FA_RANK;
    }

    @Override
    protected int getGoalType() {
        return RankConst.RankScoreType.XIAN_FA;
    }

    @Override
    public void addBattlePool(long rid) {
        XianFaData xianFaData = SysDataProvider.get(XianFaData.class);
        boolean match = xianFaData.getBattlePool().stream().anyMatch(x -> x.first == rid);
        if (!match) {
            xianFaData.getBattlePool().add(new TwoTuple<>(rid, false));
            DataCenter.updateData(xianFaData);
        }
    }

    @Override
    public void reqRandomBattleLineup(Role role, List<Integer> secretaryIds, int costGlobalId, long targetRid, int beiZhanType, long battleLogId) {
        RoleXianFa roleXianFa = role.findXianFa();
        if (roleXianFa.getBeiZhanInfo().getTargetRid() > 0) {
            log.error("仙法,请求随机仙友,正在被挑战中,玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }

        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        SecretaryData secretaryData = roleExtend.getSecretaryData();
        if (secretaryData.getSecretaryMap().isEmpty()) {
            return;
        }

        if (beiZhanType == BeiZhanType.MARK && checkMarkBeiZhan(role, targetRid)) {
            return;
        }
        if (beiZhanType == BeiZhanType.BATTLE_LOG) {
            XianFaBattleLog xianFaBattleLog = roleXianFa.getBattleLogs().stream().filter(log -> log.getLogId() == battleLogId).findFirst().orElse(null);
            if (xianFaBattleLog == null) {
                log.error("仙法,请求出站仙友,战斗日志不存在,玩家id:{},name:{},battleLogId:{}", role.getId(), role.getName(), battleLogId);
                return;
            }
        }

        long count = secretaryIds.stream().filter(id -> !secretaryData.getSecretaryMap().containsKey(id)).count();
        if (count > 0) {
            log.error("仙法,请求随机仙友,提交的仙友id错误,玩家id:{},name:{},secretaryIds:{}", role.getId(), role.getName(), secretaryIds);
            return;
        }
        if (secretaryIds.size() > GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_MAX_SHANG_ZHEN_COUNT)) {
            log.error("仙法,请求随机仙友,提交的仙友数量超过最大值,玩家id:{},name:{},size:{}", role.getId(), role.getName(), secretaryIds.size());
            return;
        }

        // 验证仙友次数是否足够（不修改状态）
        Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount = null;
        if (beiZhanType == BeiZhanType.NOTICE || beiZhanType == BeiZhanType.NORMAL_RANDOM) {
            beiZhanCount = getBeiZhanCount(role, roleXianFa, beiZhanType);
            int battleCount = beiZhanType == BeiZhanType.NORMAL_RANDOM ? GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT) : GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT);
            for (Integer secretaryId : secretaryIds) {
                TwoTuple<Integer, Integer> tuple = beiZhanCount.computeIfAbsent(secretaryId, k -> new TwoTuple<>(battleCount, 0));
                if (tuple.getFirst() <= 0) {
                    log.error("仙法,请求随机仙友,仙友次数不足,玩家id:{},name:{},secretaryId:{}", role.getId(), role.getName(), secretaryId);
                    return;
                }
            }
        }

        // 验证匹配对手是否可用
        TwoTuple<Long, Boolean> matchedElement = null;
        if (targetRid <= 0) {
            XianFaData xianFaData = SysDataProvider.get(XianFaData.class);
            List<TwoTuple<Long, Boolean>> battlePool = new ArrayList<>(xianFaData.getBattlePool());
            battlePool.removeIf(x -> x.first == role.getId());
            matchedElement = RandomUtil.randomElement(battlePool);
            if (matchedElement == null) {
                log.error("仙法,玩家请求匹配对手,随机失败,玩家id:{},name:{}", role.getId(), role.getName());
                return;
            }
            if (!matchedElement.second) {
                SecretaryData targetSecretaryData = RoleExtendManager.getInstance().getRoleExtend(matchedElement.first).getSecretaryData();
                if (targetSecretaryData.getSecretaryMap().isEmpty()) {
                    final TwoTuple<Long, Boolean> matchedElement1 = matchedElement;
                    xianFaData.getBattlePool().removeIf(x -> Objects.equals(x.first, matchedElement1.first));
                    DataCenter.updateData(xianFaData);
                    log.error("仙法,玩家请求匹配对手,目标玩家无仙友,玩家id:{},name:{},targetRid:{}", role.getId(), role.getName(), matchedElement.first);
                    return;
                }
            }
        }

        // 验证消耗道具是否足够
        int[] cost = null;
        if (costGlobalId > 0) {
            cost = GlobalUtil.findJinghaoIntArray(costGlobalId);
            if (cost.length > 1 && !BackPackStashUtil.decrease(role, cost[0], cost[1], LogAction.XIAN_FA_BATTLE_COST)) {
                log.error("仙法,请求随机仙友,消耗道具不足,玩家id:{},name:{},costGlobalId:{}", role.getId(), role.getName(), costGlobalId);
                return;
            }
        }

        // ========== 第二阶段：所有验证通过，开始修改内存状态 ==========

        BeiZhanInfo beiZhanInfo = roleXianFa.getBeiZhanInfo();

        // 扣减仙友次数
        if (beiZhanCount != null) {
            int battleCount = beiZhanType == BeiZhanType.NORMAL_RANDOM ? GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT) : GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT);
            for (Integer secretaryId : secretaryIds) {
                TwoTuple<Integer, Integer> beiZhanTuple = beiZhanCount.computeIfAbsent(secretaryId, k -> new TwoTuple<>(battleCount, 0));
                beiZhanTuple.setFirst(beiZhanTuple.getFirst() - 1);
            }
            updateBattleCount(beiZhanCount);
        }

        // 设置选中的仙友
        for (Integer secretaryId : secretaryIds) {
            TwoTuple<String, Integer> tuple = new TwoTuple<>(secretaryData.getSecretaryIncomeMap().get(secretaryId), secretaryData.getSecretaryMap().get(secretaryId).getFirst());
            beiZhanInfo.getSelectXianYou().put(secretaryId, tuple);
        }

        // 设置备战类型
        beiZhanInfo.setType(beiZhanType);

        // 扣除消耗道具并设置胜利积分倍率
        if (cost != null) {
            beiZhanInfo.setWinScoreRate(cost[2]);
        }

        // 设置对手信息
        if (targetRid > 0) {
            handleSpecificTargetRole(targetRid, beiZhanInfo);
        } else {
            if (matchedElement.second) {
                handleMonsterTarget(matchedElement, beiZhanInfo);
            } else {
                long randomTargetRid = matchedElement.first;
                handleSpecificTargetRole(randomTargetRid, beiZhanInfo);
            }
        }

        // 更新战斗日志状态
        if (beiZhanType == BeiZhanType.BATTLE_LOG) {
            roleXianFa.getBattleLogs().stream()
                    .filter(log -> log.getLogId() == battleLogId).findFirst()
                    .ifPresent(x -> x.setBattle(true));
        }

        // 保存数据并发送消息
        DataCenter.updateData(roleXianFa);
        sendRandomBattleMsg(role);
    }

    @Override
    public void reqBeiZhan(Role role, int beiZhanType) {
        RoleXianFa roleXianFa = role.findXianFa();

        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        SecretaryData secretaryData = roleExtend.getSecretaryData();
        if (secretaryData.getSecretaryMap().isEmpty()) {
            log.error("仙法,请求出站仙友,仙友为空,玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }

        Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount = getBeiZhanCount(role, roleXianFa, beiZhanType);
        int battleCount = beiZhanType == BeiZhanType.NORMAL_RANDOM ? GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT) : GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT);

        XianFaProtos.ResBeiZhanInfo beiZhanInfo = XianFaProtos.ResBeiZhanInfo.newBuilder()
                .addAllData(secretaryData.getSecretaryMap().entrySet().stream().map(entry ->
                        XianFaProtos.BeiZhanData.newBuilder()
                                .setSecretaryId(entry.getKey())
                                .setSecretaryLevel(entry.getValue().getFirst())
                                .setSecretaryCount(beiZhanCount.getOrDefault(entry.getKey(), new TwoTuple<>(battleCount, 0)).getFirst())
                                .setRefreshTime(beiZhanCount.getOrDefault(entry.getKey(), new TwoTuple<>(0, 0)).getSecond())
                                .setSecretaryIncome(secretaryData.getSecretaryIncomeMap().get(entry.getKey()))
                                .build()).collect(Collectors.toList())).build();
        ResBeiZhanInfoMessage message = new ResBeiZhanInfoMessage();
        message.setProto(beiZhanInfo);
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqBattleJiLi(Role role) {
        RoleXianFa roleXianFa = role.findXianFa();
        BeiZhanInfo beiZhanInfo = roleXianFa.getBeiZhanInfo();
        if (beiZhanInfo.getSelectXianYou().isEmpty()) {
            log.error("仙法,请求备战激励,仙友为空,玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        XianFaJiLiConfig xianFaJiLiConfig = ConfigDataManager.getInstance().getById(XianFaJiLiConfig.class,  beiZhanInfo.getJiaChengId() + 1);
        if (xianFaJiLiConfig == null) {
            log.error("仙法,请求备战激励,配置不存在,玩家id:{},name:{},configId:{}", role.getId(), role.getName(), beiZhanInfo.getJiaChengId() + 1);
            return;
        }
        Map<Integer, Long> clickIncome = calJiLiCost(role);
        HashMap<Integer, Long> newClickIncome = new HashMap<>(clickIncome);
        for (Map.Entry<Integer, Long> entry : newClickIncome.entrySet()) {
            entry.setValue(entry.getValue() * xianFaJiLiConfig.getCost() / 10000);
        }
        if (!BackPackStashUtil.decrease(role, newClickIncome, 1, LogAction.BUY_XIAN_FA_JI_LI)) {
            log.error("请求备战激励,消耗道具不足,roleId:{},roleName:{},cid:{}", role.getId(), role.getName(), xianFaJiLiConfig.getId());
            return;
        }
        beiZhanInfo.setJiaChengId(beiZhanInfo.getJiaChengId() + 1);
        DataCenter.updateData(roleXianFa);

        ResBattleJiLiMessage message = new ResBattleJiLiMessage();
        message.setProto(XianFaProtos.ResBattleJiLi.newBuilder()
                .setTotalIncome(beiZhanInfo.getTotalIncome())
                .setTotalJiaCheng(beiZhanInfo.getJiaChengId())
                .addAllClickIncome(clickIncome.entrySet().stream().map(entry ->
                                AbcProtos.CommonKeyValueLongBean.newBuilder().setKey(entry.getKey()).setValue(entry.getValue()).build())
                        .collect(Collectors.toList()))
                .build());
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqBattle(Role role) {
        RoleXianFa roleXianFa = role.findXianFa();
        BeiZhanInfo beiZhanInfo = roleXianFa.getBeiZhanInfo();
        if (beiZhanInfo.getSelectXianYou().isEmpty()) {
            log.error("仙法,请求战斗,仙友为空,玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }

        XianFaProtos.ResBattleInfo.Builder builder = reqBattle(role, beiZhanInfo);

        roleXianFa.setBeiZhanInfo(new BeiZhanInfo());
        DataCenter.updateData(roleXianFa);

        ResBattleInfoMessage message = new ResBattleInfoMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role.getId());
    }

    private XianFaProtos.ResBattleInfo.Builder reqBattle(Role role, BeiZhanInfo beiZhanInfo) {
        XianFaProtos.ResBattleInfo.Builder builder = XianFaProtos.ResBattleInfo.newBuilder();
        builder.setBeiZhanType(beiZhanInfo.getType());
        XianFaJiLiConfig xianFaJiLiConfig = ConfigDataManager.getInstance().getById(XianFaJiLiConfig.class, beiZhanInfo.getJiaChengId());
        XianFaProtos.XianFaFighterBean.Builder attrBean = XianFaProtos.XianFaFighterBean.newBuilder()
                .setRoleId(role.getId())
                .setRobotCfgId(-1)
                .setRoleName(role.getName())
                .addAllSecretary(beiZhanInfo.getSelectXianYou().entrySet().stream().map(entry -> {
                            BigInteger bigInteger = new BigInteger(entry.getValue().first);
                            if (xianFaJiLiConfig != null) {
                                bigInteger = bigInteger.add(bigInteger.multiply(BigInteger.valueOf(xianFaJiLiConfig.getJiacheng())).divide(BigInteger.valueOf(10000)));
                            }
                            return AbcProtos.CommonKeyValueStringBean.newBuilder().setKey(entry.getKey()).setValue(bigInteger.toString()).build();
                        })
                        .collect(Collectors.toList())
                );
        XianFaProtos.XianFaFighterBean.Builder targetBean = XianFaProtos.XianFaFighterBean.newBuilder()
                .setRoleId(beiZhanInfo.isRobot() ? -1 : beiZhanInfo.getTargetRid())
                .setRoleName(beiZhanInfo.getTargetName())
                .setRobotCfgId((int) beiZhanInfo.getTargetRid())
                .addAllSecretary(beiZhanInfo.getTargetXianYou().entrySet().stream().map(entry ->
                                AbcProtos.CommonKeyValueStringBean.newBuilder().setKey(entry.getKey()).setValue(entry.getValue().first).build())
                        .collect(Collectors.toList())
                );
        if (!beiZhanInfo.isRobot()) {
            targetBean.addAllFashions(QueryManager.getInstance().fashionsToBean(beiZhanInfo.getTargetTouXiang()));
        }

        // 对己方仙友排序
        LinkedHashMap<Integer, BigInteger> sortedAttrMap = getSortedXianYou(beiZhanInfo.getSelectXianYou(), beiZhanInfo.getJiaChengId());
        // 对敌方仙友排序
        LinkedHashMap<Integer, BigInteger> sortedTargetMap = getSortedXianYou(beiZhanInfo.getTargetXianYou(), 0);

        int myWinScore = 0;
        int targetFailScore = 0;
        Map<Integer, Integer> myWinXianYouMap = new HashMap<>();
        for (Map.Entry<Integer, BigInteger> targetEntry : sortedTargetMap.entrySet()) {
            for (Map.Entry<Integer, BigInteger> attrEntry : sortedAttrMap.entrySet()) {
                if (attrEntry.getValue().compareTo(BigInteger.ZERO) <= 0) {
                    continue;
                }
                // 克制
                if (beiZhanInfo.isRobot()) {
                    SecretaryConfig secretaryConfig = ConfigDataManager.getInstance().getById(SecretaryConfig.class, attrEntry.getKey());
                    if(secretaryConfig == null) {
                        continue;
                    }
                    XianFaMonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(XianFaMonsterConfig.class, beiZhanInfo.getTargetRid());
                    if (monsterConfig == null) {
                        continue;
                    }
                    Pair<BigInteger, BigInteger> xiuWeiPair = SecretaryManager.getInstance().calSecretaryRestrainRelation(secretaryConfig.getId(), attrEntry.getValue(), monsterConfig.getRestrainType()[0], BigInteger.ZERO);
                    attrEntry.setValue(xiuWeiPair.getKey());
                    //TODO
                    //怪物 = xiuWeiPair.getValue();
                }
                int compareResult = attrEntry.getValue().compareTo(targetEntry.getValue());
                // 胜利
                if (compareResult > 0) {
                    attrEntry.setValue(attrEntry.getValue().subtract(targetEntry.getValue()));
                    targetEntry.setValue(BigInteger.ZERO);
                    myWinScore += 2;
                    targetFailScore += 1;
                    myWinXianYouMap.merge(attrEntry.getKey(), 1, Integer::sum);
                    break;
                } else if (compareResult < 0) { // 失败
                    targetEntry.setValue(targetEntry.getValue().subtract(attrEntry.getValue()));
                    attrEntry.setValue(BigInteger.ZERO);
                } else { // 平局
                    attrEntry.setValue(BigInteger.ZERO);
                    targetEntry.setValue(BigInteger.ZERO);
                    myWinScore += 2;
                    targetFailScore += 1;
                    myWinXianYouMap.merge(attrEntry.getKey(), 1, Integer::sum);
                    break;
                }
            }
        }
        // 有积分就更新排行榜
        if (myWinScore > 0) {
            BigInteger myWinScoreBig = BigInteger.valueOf(myWinScore).multiply(BigInteger.valueOf(beiZhanInfo.getWinScoreRate()));
            updateScore(role, getGoalType(), myWinScoreBig, false);
            myWinScore = myWinScoreBig.intValue();
        }
        if (!beiZhanInfo.isRobot() && targetFailScore > 0) {
            Role targetRole = DataCenter.get(Role.class, beiZhanInfo.getTargetRid());
            decScore(targetRole, getGoalType(), BigInteger.valueOf(targetFailScore));
        }

        // 生成对方战报
        if (!beiZhanInfo.isRobot()) {
            Role targetRole = DataCenter.get(Role.class, beiZhanInfo.getTargetRid());
            RoleXianFa targetRoleXianFa = targetRole.findXianFa();
            targetRoleXianFa.addBattleLog(
                    role.getId(),
                    role.getName(),
                    targetFailScore < sortedTargetMap.size(),
                    TimeUtil.getNowOfSeconds(),
                    role.getRoleAdvance().getAppearance().getWears(),
                    targetFailScore,
                    targetRole.getZhuanShengId(),
                    queryScore(role, getGoalType()));
            DataCenter.updateData(targetRoleXianFa);
        }

        int sum = myWinXianYouMap.values().stream().mapToInt(Integer::intValue).sum();
        if (!beiZhanInfo.isRobot()) {
            XianFaData xianFaData = SysDataProvider.get(XianFaData.class);
            // 判断是否上公告
            if (sum >= GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_CONDITION)) {
                List<XianFaNoticeRecord> noticeRecords = xianFaData.getNoticeRecords();
                XianFaNoticeRecord record = XianFaNoticeRecord.valueOf(role.getId(), beiZhanInfo.getTargetRid(), TimeUtil.getNowOfSeconds(), sum);
                if (noticeRecords.size() > GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_MAX_COUNT)) {
                    noticeRecords.remove(0);
                }
                noticeRecords.add(record);
                RoleXianFa roleXianFa = role.findXianFa();
                roleXianFa.setLastNoticeBattleRid(beiZhanInfo.getTargetRid());
            }
            // 追击固定奖励
            if (beiZhanInfo.getType() == BeiZhanType.MARK) {
                List<MarkData> markRecord = xianFaData.getMarkRecord();
                MarkData markData = markRecord.stream().filter(x -> x.getTargetRid() == (beiZhanInfo.getTargetRid()) && !x.isOver()).findFirst().orElse(null);
                if (markData != null) {
                    markData.getJoinRids().add(new TwoTuple<>(role.getId(), sum));
                    CountManager.getInstance().count(role, CountConst.CountType.XIAN_FA_ZHUI_JI_COUNT);
                    Map<Integer, Long> rewardMap = GlobalUtil.toJinHaoAndYuHaoMapIntLong(GameConst.GlobalId.XIAN_FA_ZHUI_JI_REWARD);
                    BackPackStashUtil.increase(role, rewardMap, LogAction.XIAN_FA_MARK_ZHUI_JI, false);
                }
            }
            DataCenter.updateData(xianFaData);
        }

        // 找出胜利次数最多的仙友
        int max = myWinXianYouMap.values().stream().max(Comparator.naturalOrder()).orElse(0);
        if (max > 0) {
            Map<Integer, Long> rewardMap = GlobalUtil.toJinHaoAndYuHaoMapIntLong(GameConst.GlobalId.XIAN_FA_BATTLE_REWARD_MULTIPLE);
            for (Map.Entry<Integer, Long> entry : rewardMap.entrySet()) {
                entry.setValue(entry.getValue() * max);
            }
            BackPackStashUtil.increase(role, rewardMap, LogAction.XIAN_FA_BATTLE_REWARD, false);
            // 给仙友提升固定修为
            if (beiZhanInfo.getType() != BeiZhanType.NORMAL_RANDOM && !beiZhanInfo.isRobot()) {
                RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
                SecretaryData secretaryData = roleExtend.getSecretaryData();
                for (Integer id : beiZhanInfo.getSelectXianYou().keySet()) {
                    Map<Integer, Long> map = secretaryData.getSecretaryXianFaXiuWei().computeIfAbsent(id, k -> new HashMap<>());
                    int defaultValue = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_ADD_XIU_WEI_VALUE);
                    BigInteger bigInteger = BigInteger.valueOf(defaultValue).multiply(BigInteger.valueOf(max));
                    MapUtil.merge(ItemCoinUtil.getCoinItemsMap(bigInteger), map);

                    builder.addSecretary(AbcProtos.CommonKeyValueStringBean.newBuilder().setKey(id).setValue(bigInteger.toString()).build());
                }
                SecretaryManager.getInstance().calSecretaryTotalIncome(role, secretaryData, -1);
            }
        }
        CountManager.getInstance().count(role, CountConst.CountType.XIAN_FA_BATTLE_TOTAL_COUNT);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_FA_BATTLE_COUNT);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_FA_BATTLE_TOTAL_COUNT);

        return builder.setMyWinScore(myWinScore)
                .setTargetFailScore(targetFailScore)
                .setWinCount(max)
                .setAttWin(myWinScore > 0)
                .setAtt(attrBean)
                .setDef(targetBean);
    }

    @Override
    public void reqRandomInfo(Role role) {
        sendRandomBattleMsg(role);
    }

    @Override
    public void reqScoreRankInfo(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingBean>> query = queryRanking(role, 1, GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_RANK_MAX_COUNT));
        ResScoreRankInfoMessage msg = new ResScoreRankInfoMessage();
        msg.setProto(XianFaProtos.ResScoreRankInfo.newBuilder()
                .setSelfScore(query.getFirst().toString())
                .setSelfRanking(query.second)
                .addAllRankings(query.third)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqLingDiInfo(Role role) {
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        RoleXianFa roleXianFa = role.findXianFa();

        XianFaProtos.ResLingDiInfo.Builder builder = XianFaProtos.ResLingDiInfo.newBuilder().setId(roleXianFa.getLingDi().first).setLastRewardTime(roleXianFa.getLingDi().second);
        XianFaLingDiConfig lingDiConfig = ConfigDataManager.getInstance().getById(XianFaLingDiConfig.class, roleXianFa.getLingDi().first);
        if (lingDiConfig != null && lingDiConfig.getXiuweijiacheng() > 0) {
            int lastSecond = nowOfSeconds - roleXianFa.getLingDi().second;
            builder.setOver(lastSecond >= lingDiConfig.getTime());
            Map<Integer, Long> clickIncome = calClickIncome(role, lingDiConfig, nowOfSeconds);
            builder.addAllCurIncome(clickIncome.entrySet().stream().map(entry ->
                            AbcProtos.CommonKeyValueLongBean.newBuilder().setKey(entry.getKey()).setValue(entry.getValue()).build())
                    .collect(Collectors.toList()));
        }

        ResLingDiInfoMessage msg = new ResLingDiInfoMessage();
        msg.setProto(builder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqLingDiUp(Role role) {
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        RoleXianFa roleXianFa = role.findXianFa();
        XianFaLingDiConfig lingDiConfig = ConfigDataManager.getInstance().getById(XianFaLingDiConfig.class, roleXianFa.getLingDi().first);
        if (lingDiConfig == null) {
            log.error("仙法,请求领地升级,当前领地等级找不到配置,roleId:{},roleName:{},lingDiId:{}", role.getId(), role.getName(), roleXianFa.getLingDi().first);
            return;
        }
        XianFaLingDiConfig nextLingDiConfig = ConfigDataManager.getInstance().getById(XianFaLingDiConfig.class, roleXianFa.getLingDi().first + 1);
        if (nextLingDiConfig == null) {
            log.error("仙法,请求领地升级,当前领地已经最大级,roleId:{},roleName:{},lingDiId:{}", role.getId(), role.getName(), roleXianFa.getLingDi().first);
            return;
        }
        if (!BackPackStashUtil.decrease(role, lingDiConfig.getCost(), LogAction.XIAN_FA_LING_DI_UP)) {
            log.error("仙法,请求领地升级,消耗道具不足,roleId:{},roleName:{},cid:{}", role.getId(), role.getName(), lingDiConfig.getId());
            return;
        }

        Map<Integer, Long> clickIncome = role.findNormal().getRenderData().getClickIncome();
        for (Map.Entry<Integer, Long> entry : clickIncome.entrySet()) {
            entry.setValue((entry.getValue() + entry.getValue() * nextLingDiConfig.getXiuweijiacheng() / 10000) * nextLingDiConfig.getTime());
        }
        BackPackStashUtil.increase(role, clickIncome, LogAction.XIAN_FA_LING_DI_UP, true);

        roleXianFa.setLingDi(new TwoTuple<>(nextLingDiConfig.getId(), nowOfSeconds));
        DataCenter.updateData(roleXianFa);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_FA_LING_DI_LEVEL);

        reqLingDiInfo(role);
    }

    @Override
    public void reqLingDiReward(Role role) {
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        RoleXianFa roleXianFa = role.findXianFa();
        XianFaLingDiConfig lingDiConfig = ConfigDataManager.getInstance().getById(XianFaLingDiConfig.class, roleXianFa.getLingDi().first);
        if (lingDiConfig == null || lingDiConfig.getXiuweijiacheng() <= 0) {
            return;
        }
        Map<Integer, Long> clickIncome = calClickIncome(role, lingDiConfig, nowOfSeconds);
        BackPackStashUtil.increase(role, clickIncome, LogAction.XIAN_FA_LING_DI_REWARD, true);
        roleXianFa.getLingDi().setSecond(nowOfSeconds);
        DataCenter.updateData(roleXianFa);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_FA_LING_DI_REWARD);
        reqLingDiInfo(role);
    }

    @Override
    public void reqBattleLog(Role role) {
        RoleXianFa roleXianFa = role.findXianFa();

        ResBattleLogMessage msg = new ResBattleLogMessage();
        msg.setProto(XianFaProtos.ResBattleLog.newBuilder()
                .addAllData(roleXianFa.getBattleLogs().stream().map(log ->
                                XianFaProtos.BattleLogData.newBuilder()
                                        .setBattleLogId(log.getLogId())
                                        .setTargetRid(log.getTargetRId())
                                        .setTargetName(log.getTargetName())
                                        .setWin(log.isWin())
                                        .setTime(log.getTime())
                                        .addAllTargetFashions(QueryManager.getInstance().fashionsToBean(log.getTargetFashions()))
                                        .setMyFailCount(log.getMyFailCount())
                                        .setTargetZhuanShengId(log.getTargetZhuanShengId())
                                        .setTargetScore(log.getTargetScore())
                                        .setIsBattle(log.isBattle())
                                        .build())
                        .collect(Collectors.toList()))
                .build());

        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void initXianFaRobot() {
        XianFaData data = SysDataProvider.get(XianFaData.class);
        data.getBattlePool().clear();
        List<XianFaMonsterConfig> monsterConfigs = ConfigDataManager.getInstance().getList(XianFaMonsterConfig.class);
        monsterConfigs.forEach(config -> data.getBattlePool().add(new TwoTuple<>(config.getId(), true)));
        DataCenter.updateData(data);
    }

    @Override
    public void reqNoticeRecord(Role role) {
        XianFaData data = SysDataProvider.get(XianFaData.class);
        RoleXianFa roleXianFa = role.findXianFa();
        RoleSummary summary = SummaryManager.getInstance().getSummary(roleXianFa.getLastNoticeBattleRid());

        XianFaProtos.ResNoticeRecord.Builder builder = XianFaProtos.ResNoticeRecord.newBuilder();
        Optional.ofNullable(summary)
                .ifPresent(s -> {
                    builder.setLastTargetName(s.getName());
                    builder.setLastTargetRid(s.getId());
                });
        data.getNoticeRecords().forEach(record -> {
            RoleSummary roleSummary = SummaryManager.getInstance().getSummary(record.getRid());
            RoleSummary targetSummary = SummaryManager.getInstance().getSummary(record.getTargetRid());
            XianFaProtos.NoticeRecord noticeRecord = XianFaProtos.NoticeRecord.newBuilder().setRid(record.getRid())
                    .setRoleName(roleSummary.getName()).addAllFashions(QueryManager.getInstance().fashionsToBean(roleSummary.getData().getFashions()))
                    .setTargetRid(record.getTargetRid())
                    .setTargetName(targetSummary.getName())
                    .setTime(record.getTime())
                    .setXianYouCount(record.getXianYouCount()).build();
            builder.addRecords(noticeRecord);
        });

        ResNoticeRecordMessage msg = new ResNoticeRecordMessage();
        msg.setProto(builder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqXianFaKuaiSuFight(Role role, List<XianFaProtos.XianFaLineupBean> lineupsList) {
        if(CollectionUtils.isEmpty(lineupsList) || lineupsList.size() > GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_KUAISU_LINEUP_MAX_COUNT)) {
            return;
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        SecretaryData secretaryData = roleExtend.getSecretaryData();
        if (secretaryData.getSecretaryMap().isEmpty()) {
            log.error("仙法,快速战斗,仙友为空,玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        RoleXianFa roleXianFa = role.findXianFa();
        Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount = getBeiZhanCount(role, roleXianFa, BeiZhanType.NORMAL_RANDOM);
        int battleCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT);

        List<XianFaJiLiConfig> allConfigs = ConfigDataManager.getInstance().getList(XianFaJiLiConfig.class);
        XianFaJiLiConfig maxJiLiConfig = allConfigs.stream().max(Comparator.comparing(XianFaJiLiConfig::getId)).get();
        long totalCost = allConfigs.stream().mapToLong(XianFaJiLiConfig::getCost).sum();

        Map<Integer, Long> jiliTotalCost = calJiLiCost(role);
        for (Map.Entry<Integer, Long> entry : jiliTotalCost.entrySet()) {
            entry.setValue(entry.getValue() * totalCost / 10000);
        }
        ItemCoinUtil.refreshCoinBigNumCarry(jiliTotalCost);

        XianFaData xianFaData = SysDataProvider.get(XianFaData.class);
        List<TwoTuple<Long, Boolean>> battlePool = new ArrayList<>(xianFaData.getBattlePool());
        battlePool.removeIf(x -> x.first == role.getId());

        XianFaProtos.ResXianFaKuaiSuFight.Builder resBuilder = XianFaProtos.ResXianFaKuaiSuFight.newBuilder();
        for (XianFaProtos.XianFaLineupBean lineup : lineupsList) {
            List<Integer> secretaryIds = lineup.getSecretaryCfgIdList();
            if(CollectionUtils.isEmpty(secretaryIds)) {
                continue;
            }

            boolean countLack = false;
            for (Integer secretaryId : secretaryIds) {
                TwoTuple<Integer, Integer> tuple = beiZhanCount.computeIfAbsent(secretaryId, k -> new TwoTuple<>(battleCount, 0));
                if (tuple.getFirst() <= 0) {
                    countLack = true;
                    log.error("仙法,快速战斗,仙友次数不足,玩家id:{},name:{},secretaryId:{}", role.getId(), role.getName(), secretaryId);
                    break;
                }
            }
            if(countLack) {
                continue;
            }

            TwoTuple<Long, Boolean> element = RandomUtil.randomElement(battlePool);
            if (element == null) {
                log.error("仙法,快速战斗匹配对手,随机失败,玩家id:{},name:{}", role.getId(), role.getName());
                continue;
            }

            BeiZhanInfo beiZhanInfo = new BeiZhanInfo();
            beiZhanInfo.setType(BeiZhanType.NORMAL_RANDOM);
            secretaryIds.forEach(secretaryId-> {
                TwoTuple<String, Integer> tuple = new TwoTuple<>(secretaryData.getSecretaryIncomeMap().get(secretaryId), secretaryData.getSecretaryMap().get(secretaryId).getFirst());
                beiZhanInfo.getSelectXianYou().put(secretaryId, tuple);
                TwoTuple<Integer, Integer> beiZhanTuple = beiZhanCount.computeIfAbsent(secretaryId, k -> new TwoTuple<>(battleCount, 0));
                beiZhanTuple.setFirst(beiZhanTuple.getFirst() - 1);
            });
            updateBattleCount(beiZhanCount);

            //激励
            if (lineup.getGuwu() && BackPackStashUtil.decrease(role, jiliTotalCost, 1, LogAction.BUY_XIAN_FA_JI_LI)) {
                beiZhanInfo.setJiaChengId(maxJiLiConfig.getId());
            }

            if (element.second) {
                handleMonsterTarget(element, beiZhanInfo);
            } else {
                long randomTargetRid = element.first;
                handleSpecificTargetRole(randomTargetRid, beiZhanInfo);
            }

            XianFaProtos.ResBattleInfo.Builder builder = reqBattle(role, beiZhanInfo);
            resBuilder.addResults(builder);
        }
        DataCenter.updateData(roleXianFa);

        ResXianFaKuaiSuFightMessage msg = new ResXianFaKuaiSuFightMessage();
        msg.setProto(resBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void ReqMarkRecords(Role role) {
        XianFaData data = SysDataProvider.get(XianFaData.class);

        List<XianFaProtos.MarkRecordData> list = new ArrayList<>();
        data.getMarkRecord().forEach(markData ->{
            Role publishRole = DataCenter.get(Role.class, markData.getRid());
            Role markRole = DataCenter.get(Role.class, markData.getTargetRid());
            XianFaProtos.MarkRecordData.Builder builder = XianFaProtos.MarkRecordData.newBuilder().setPublishRid(markData.getRid()).setPublishName(publishRole.getName())
                    .setMarkRid(markData.getTargetRid()).setMarkName(markRole.getName()).setMarkZhuanShengId(markRole.getZhuanShengId())
                    .addAllMarkFashions(QueryManager.getInstance().fashionsToBean(markRole.getRoleAdvance().getAppearance().getWears()))
                    .setTime(markData.getTime()).setIsOver(markData.isOver());
            markData.getJoinRids().forEach(tuple -> {
                Role joinRole = DataCenter.get(Role.class, tuple.getFirst());
                builder.addMarkJoinData(XianFaProtos.MarkJoinData.newBuilder().setJoinRid(joinRole.getId())
                        .setJoinName(joinRole.getName()).setJoinZhuanShengId(joinRole.getZhuanShengId())
                        .addAllMarkFashions(QueryManager.getInstance().fashionsToBean(joinRole.getRoleAdvance().getAppearance().getWears()))
                        .setXianYouCount(tuple.getSecond()).build());
            });
            list.add(builder.build());

        });
        ResMarkRecordsMessage msg = new ResMarkRecordsMessage();
        msg.setProto(XianFaProtos.ResMarkRecords.newBuilder().addAllRecords(list)
                .setCurDayCount(CountManager.getInstance().getCount(role, CountConst.CountType.XIAN_FA_ZHUI_JI_COUNT)).build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void ReqPublishMark(Role role, long targetRid, int costGlobalId) {
        XianFaData data = SysDataProvider.get(XianFaData.class);
        List<MarkData> markRecord = data.getMarkRecord();

        int[] cost = GlobalUtil.findJinghaoIntArray(costGlobalId);
        if (cost.length < 4) {
            log.error("仙法,发布标记,配置消耗错误,roleId:{},roleName:{},costGlobalId:{}", role.getId(), role.getName(), costGlobalId);
            return;
        }
        long count = markRecord.stream().filter(x -> x.getTargetRid() == (targetRid) && !x.isOver()).count();
        if (count > 0) {
            log.error("仙法,该玩家标记中还未结束,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return;
        }
        if (!BackPackStashUtil.decrease(role, cost[0], cost[1], LogAction.XIAN_FA_PUBLISH_MARK_COST)) {
            log.error("仙法,发布标记,消耗道具不足,roleId:{},roleName:{},costGlobalId:{}", role.getId(), role.getName(), costGlobalId);
            return;
        }
        MarkData markData = new MarkData();
        markData.setTargetRid(targetRid);
        markData.setTime(TimeUtil.getNowOfSeconds());
        markData.setRewardRate(cost[2]);
        markData.setScoreRate(cost[3]);
        markData.setRid(role.getId());
        markRecord.add(markData);

        DataCenter.updateData(data);

        ReqMarkRecords(role);
    }

    @Override
    public void reqHasBeiZhan(Role role) {
        ResHasBeiZhanMessage msg = new ResHasBeiZhanMessage();
        msg.setProto(XianFaProtos.ResHasBeiZhan.newBuilder()
                .setHasBeiZhan(role.findXianFa().getBeiZhanInfo().getTargetRid() > 0).build());

        MessageUtil.sendMsg(msg, role.getId());
    }

    private Map<Integer, Long> calClickIncome(Role role, XianFaLingDiConfig lingDiConfig, int nowOfSeconds) {
        RoleXianFa roleXianFa = role.findXianFa();
        int min = Math.min((nowOfSeconds - roleXianFa.getLingDi().second), lingDiConfig.getTime());
        if (min > 0) {
            Map<Integer, Long> clickIncome = role.findNormal().getRenderData().getClickIncome();
            for (Map.Entry<Integer, Long> entry : clickIncome.entrySet()) {
                entry.setValue((entry.getValue() + entry.getValue() * lingDiConfig.getXiuweijiacheng() / 10000) * min);
            }
            return clickIncome;
        }
        return new HashMap<>();
    }

    @Override
    public void onSeverStartUp() {
        XianFaData data = SysDataProvider.get(XianFaData.class);
        if (data.getBattlePool().isEmpty()) {
            List<XianFaMonsterConfig> monsterConfigs = ConfigDataManager.getInstance().getList(XianFaMonsterConfig.class);
            monsterConfigs.forEach(config -> data.getBattlePool().add(new TwoTuple<>(config.getId(), true)));
            DataCenter.updateData(data);
        }
    }

    private void sendRandomBattleMsg(Role role) {
        BeiZhanInfo beiZhanInfo = role.findXianFa().getBeiZhanInfo();

        ResRandomBattleMessage message = new ResRandomBattleMessage();
        XianFaProtos.ResRandomBattle.Builder builder = XianFaProtos.ResRandomBattle.newBuilder();
        if (!beiZhanInfo.getSelectXianYou().isEmpty()) {
            builder.setTargetRid(beiZhanInfo.getTargetRid())
                    .setTargetName(beiZhanInfo.getTargetName())
                    .setIsRobot(beiZhanInfo.isRobot())
                    .addAllTargetData(beiZhanInfo.getTargetXianYou().entrySet().stream().map(entry ->
                            XianFaProtos.BeiZhanData.newBuilder()
                                    .setSecretaryId(entry.getKey())
                                    .setSecretaryIncome(entry.getValue().first)
                                    .setSecretaryLevel(entry.getValue().second)
                                    .build()).collect(Collectors.toList()))
                    .setTotalIncome(beiZhanInfo.getTotalIncome())
                    .addAllClickIncome(calJiLiCost(role).entrySet().stream().map(entry ->
                                    AbcProtos.CommonKeyValueLongBean.newBuilder().setKey(entry.getKey()).setValue(entry.getValue()).build())
                            .collect(Collectors.toList()))
                    .setJiaCheng(beiZhanInfo.getJiaChengId());
            if (!beiZhanInfo.isRobot()) {
                builder.setTargetZhuanShengId(beiZhanInfo.getTargetZhuanShengId());
                builder.addAllTargetFashions(QueryManager.getInstance().fashionsToBean(beiZhanInfo.getTargetTouXiang()));
            }
        }
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role.getId());
    }

    private LinkedHashMap<Integer, BigInteger> getSortedXianYou(Map<Integer, TwoTuple<String, Integer>> xianYouMap, int jiaChengId) {
        XianFaJiLiConfig xianFaJiLiConfig = ConfigDataManager.getInstance().getById(XianFaJiLiConfig.class, jiaChengId);
        return xianYouMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(
                        (s1, s2) -> new BigInteger(s2.first).compareTo(new BigInteger(s1.first))))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            BigInteger bigInteger = new BigInteger(entry.getValue().first);
                            if (xianFaJiLiConfig != null) {
                                bigInteger = bigInteger.add(bigInteger.multiply(BigInteger.valueOf(xianFaJiLiConfig.getJiacheng())).divide(BigInteger.valueOf(10000)));
                            }
                            return bigInteger;
                        },
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                ));
    }

    private void handleSpecificTargetRole(long targetRid, BeiZhanInfo beiZhanInfo) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(targetRid);
        RoleExtend targetRoleExtend = RoleExtendManager.getInstance().getRoleExtend(targetRid);
        SecretaryData targetSecretaryData = targetRoleExtend.getSecretaryData();

        beiZhanInfo.setRobot(false);
        beiZhanInfo.setTargetRid(targetRid);
        beiZhanInfo.setTargetName(summary.getName());
        beiZhanInfo.setTargetTouXiang(summary.getData().getFashions());
        beiZhanInfo.setTargetZhuanShengId(summary.getZhuanShengId());

        Map<Integer, TwoTuple<String, Integer>> xianYouMap = new HashMap<>();
        targetSecretaryData.getSecretaryMap().forEach((id, tuple) -> {
            xianYouMap.put(id, new TwoTuple<>(targetSecretaryData.getSecretaryIncomeMap().get(id), tuple.first));
        });
        beiZhanInfo.setTargetXianYou(xianYouMap);
    }

    private void updateBattleCount(Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount) {
        beiZhanCount.forEach((key, value) -> {
            if (value.getFirst() <= 0 && value.getSecond() <= 0) {
                value.setSecond(TimeUtil.getNowOfSeconds() + GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT_TIME_GAP));
            }
        });
    }

    private void handleMonsterTarget(TwoTuple<Long, Boolean> element, BeiZhanInfo beiZhanInfo) {
        XianFaMonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(XianFaMonsterConfig.class, element.first);
        beiZhanInfo.setRobot(true);
        beiZhanInfo.setTargetRid(element.first);
        beiZhanInfo.setTargetName(monsterConfig.getName());

        for (int i = 0; i < monsterConfig.getAttitude().size(); i++) {
            beiZhanInfo.getTargetXianYou().put(i + 1, new TwoTuple<>(monsterConfig.getAttitude().get(i).toString(), monsterConfig.getLevel().get(i)));
        }
    }

    private Map<Integer, TwoTuple<Integer, Integer>> getBeiZhanCount(Role role, RoleXianFa roleXianFa, int beiZhanType) {
        Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount;
        if (beiZhanType == BeiZhanType.NORMAL_RANDOM) {
            beiZhanCount = roleXianFa.getBeiZhanCount();
        } else {
            beiZhanCount = roleXianFa.getNoticeBeiZhanCount();
        }

        if (beiZhanCount.isEmpty()) {
            return beiZhanCount;
        }
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.XIAN_FA_XIAN_YOU_REFRESH_COUNT, beiZhanType);
        if (count >= GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_RECOVER_COUNT)) {
            for (Map.Entry<Integer, TwoTuple<Integer, Integer>> entry : beiZhanCount.entrySet()) {
                // dyq策划说 到了最大限制后 倒计时时间设为第二天零点
                if (entry.getValue().getFirst() <= 0) {
                    entry.getValue().setSecond((int) TimeUtil.getTodayZeroFromNowHourSeconds(24));
                }
            }
            DataCenter.updateData(roleXianFa);
            return beiZhanCount;
        }
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        int normalRandomCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT);
        int otherCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT);
        // 刷新仙友上阵冷却
        boolean isUpdate = false;
        for (Map.Entry<Integer, TwoTuple<Integer, Integer>> entry : beiZhanCount.entrySet()) {
            TwoTuple<Integer, Integer> tuple = entry.getValue();
            if (tuple.getSecond() <= 0 || tuple.getSecond() >= nowOfSeconds) {
                continue;
            }
            tuple.setFirst(beiZhanType == BeiZhanType.NORMAL_RANDOM ? normalRandomCount : otherCount);
            tuple.setSecond(0);
            isUpdate = true;
        }
        if (isUpdate) {
            DataCenter.updateData(roleXianFa);
            CountManager.getInstance().count(role, CountConst.CountType.XIAN_FA_XIAN_YOU_REFRESH_COUNT, beiZhanType, 1);
        }

        return beiZhanCount;
    }

    private boolean checkMarkBeiZhan(Role role, long targetRid) {
        if (targetRid <= 0) {
            log.error("仙法,标记备战,targetRid错误,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return true;
        }

        XianFaData data = SysDataProvider.get(XianFaData.class);
        List<MarkData> markRecord = data.getMarkRecord();
        MarkData markData = markRecord.stream().filter(x -> x.getTargetRid() == (targetRid) && !x.isOver()).findFirst().orElse(null);
        if (markData == null) {
            log.error("仙法,标记备战,该玩家未被标记中,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return true;
        }
        long findCount = markData.getJoinRids().stream().filter(tuple -> tuple.getFirst() == role.getId()).count();
        if (findCount > 0) {
            log.error("仙法,标记备战,玩家已在参与队列中,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return true;
        }
        if (markData.getJoinRids().size() >= GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_ZHUI_JI_MAX_COUNT)) {
            log.error("仙法,标记备战,该玩家标记参与人数已满,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return true;
        }
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.XIAN_FA_ZHUI_JI_COUNT);
        if (count >= GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT)) {
            log.error("仙法,标记备战,每日追击次数已满,roleId:{},roleName:{},targetRid:{}", role.getId(), role.getName(), targetRid);
            return true;
        }
        return false;
    }

    @Override
    public void scheduleUpdateOnMinute() {
        XianFaData data = SysDataProvider.get(XianFaData.class);
        if (data.getBattlePool().isEmpty()) {
            return;
        }

        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        // 刷新匹配池
        refreshBattlePool(data, nowOfSeconds);
        // 刷新发布令
        refreshMarkRecord(data, nowOfSeconds);
        DataCenter.updateData(data);
    }
    private void refreshBattlePool(XianFaData data, int nowOfSeconds) {
        List<TwoTuple<Long, Boolean>> battlePool = data.getBattlePool();
        int poolMaxLimit = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_POOL_MAX_LIMIT);
        if ((nowOfSeconds - data.getPoolRefreshTime()) / 60 >= 10) {
            if (battlePool.size() > poolMaxLimit) {
                // 优先剔除机器人
                battlePool.removeIf(element -> element.second);
            }
            // 剔除不活跃的玩家
            while (battlePool.size() > poolMaxLimit) {
                int i = 0;
                Iterator<TwoTuple<Long, Boolean>> iterator = battlePool.iterator();
                // 一次删10个 避免一下子把池子删没了
                while (i < 10 && iterator.hasNext()) {
                    i++;
                    TwoTuple<Long, Boolean> next = iterator.next();
                    Role role = DataCenter.get(Role.class, next.first);
                    if (role == null) {
                        iterator.remove();
                        continue;
                    }
                    if (role.isOnline()) {
                        continue;
                    }
                    int logoutTime = role.getRoleLogin().getLogoutTime();
                    // 超出7天删除
                    if (logoutTime > 0 && logoutTime < nowOfSeconds - 60 * 60 * 24 * 7) {
                        iterator.remove();
                    }
                }
            }
        }
        data.setPoolRefreshTime(nowOfSeconds);
    }

    private void refreshMarkRecord(XianFaData data, int nowOfSeconds) {
        List<MarkData> markRecord = data.getMarkRecord();
        for (MarkData markData : markRecord) {
            if (markData.isOver()) {
                continue;
            }
            // 判断当前时间和记录的时间是否到达24小时
            if (markData.getTime() + 24 * 60 * 60 < nowOfSeconds) {
                markData.setOver(true);
                // 给发布者额外奖励邮件
                Role role = DataCenter.get(Role.class, markData.getRid());
                Map<Integer, Long> rewardMap = GlobalUtil.toJinHaoAndYuHaoMapIntLong(GameConst.GlobalId.XIAN_FA_ZHUI_JI_REWARD);
                for (Map.Entry<Integer, Long> entry : rewardMap.entrySet()) {
                    entry.setValue(entry.getValue() * markData.getRewardRate());
                }
                BackpackStash stash = new BackpackStash(role);
                stash.increase(EmailConst.toMailAttach(rewardMap, LogAction.XIAN_FA_MARK_ZHUI_JI_EXTRA));
                stash.commitToMail2(role.getId(), GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_ZHUI_JI_REWARD_MAIL), false);
                // 给发布者额外积分
                RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(markData.getTargetRid());
                SecretaryData secretaryData = roleExtend.getSecretaryData();
                updateScore(role, getGoalType(), BigInteger.valueOf(secretaryData.getSecretaryMap().size()).multiply(BigInteger.valueOf(markData.getScoreRate())), false);
            }
        }
    }

    @Override
    public void onRoleSecond(Role role) {
        RoleXianFa roleXianFa = role.findXianFa();
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        int globalInt = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_LOG_FAIL_CLEAN);
        roleXianFa.getBattleLogs().removeIf(battleLog ->
                !battleLog.isWin() &&
                battleLog.getTime() + globalInt < nowOfSeconds);
        DataCenter.updateData(roleXianFa);

        // 给客户端红点用的
        XianFaLingDiConfig lingDiConfig = ConfigDataManager.getInstance().getById(XianFaLingDiConfig.class, roleXianFa.getLingDi().first);
        if (lingDiConfig != null && lingDiConfig.getXiuweijiacheng() > 0) {
            int lastSecond = nowOfSeconds - roleXianFa.getLingDi().second;
            if (lastSecond >= lingDiConfig.getTime()) {
                reqLingDiInfo(role);
            }
        }
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleXianFa roleXianFa = role.findXianFa();
        Map<Integer, TwoTuple<Integer, Integer>> beiZhanCount = roleXianFa.getBeiZhanCount();
        int normalRandomCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_BATTLE_COUNT);
        for (Map.Entry<Integer, TwoTuple<Integer, Integer>> entry : beiZhanCount.entrySet()) {
            TwoTuple<Integer, Integer> value = entry.getValue();
            value.setFirst(normalRandomCount);
            value.setSecond(0);
        }
        Map<Integer, TwoTuple<Integer, Integer>> noticeBeiZhanCount = roleXianFa.getNoticeBeiZhanCount();
        int otherCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_FA_NOTICE_BATTLE_RECOVER_COUNT);
        for (Map.Entry<Integer, TwoTuple<Integer, Integer>> entry : noticeBeiZhanCount.entrySet()) {
            TwoTuple<Integer, Integer> value = entry.getValue();
            value.setFirst(otherCount);
            value.setSecond(0);
        }
        DataCenter.updateData(roleXianFa);

        reqBeiZhan(role, BeiZhanType.NORMAL_RANDOM);
        reqBeiZhan(role, BeiZhanType.NOTICE);
    }

    private Map<Integer, Long> calJiLiCost(Role role) {
        Map<Integer, Long> map = new HashMap<>();
        ZhuanShengZiDongConfig config = ConfigDataManager.getInstance().getById(ZhuanShengZiDongConfig.class, role.getZhuanShengId() / 1000);
        if (config == null) {
            log.error("仙游-计算当前激励消耗#玩家:{}-{},未找到对应的配置,level:{}", role.getId(), role.getName(), role.getZhuanShengId());
            return map;
        }
        RenderData renderData = role.findNormal().getRenderData();
        Map<Integer, Long> clickIncome = renderData.getClickIncome();
        if (renderData.getQinHeData().getMultiple() > 0) {
            return clickIncome;
        }
        BigInteger coinBigInteger = ItemCoinUtil.getCoinBigInteger(clickIncome);
        coinBigInteger = new BigDecimal(coinBigInteger).multiply(BigDecimal.valueOf(config.getMultiple())).toBigInteger();
        map.putAll(ItemCoinUtil.getCoinItemsMap(coinBigInteger));
        ItemCoinUtil.refreshCoinBigNumCarry(map);
        return map;
    }
}

