package com.sh.game.common.util;

import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BackPackStashUtil {
    public static boolean decrease(Role role, int itemId, long count, LogAction logAction) {
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, count);
        return stash.commit(role, logAction);
    }

    public static boolean decrease(Role role, List<int[]> items, LogAction logAction) {
        if(CollectionUtils.isEmpty(items)) {
            return true;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(items);
        return stash.commit(role, logAction);
    }

    public static boolean decrease(Role role, List<int[]> items, double multiple, LogAction logAction) {
        if(CollectionUtils.isEmpty(items)) {
            return true;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(items, multiple);
        return stash.commit(role, logAction);
    }

    public static boolean decrease(Role role, Map<Integer, Long> itemMap, double multiple, LogAction logAction) {
        if(MapUtils.isEmpty(itemMap)) {
            return true;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemMap, multiple);
        return stash.commit(role, logAction);
    }

    public static boolean increase(Role role, Map<Integer, Long> itemMap, int multiple, LogAction logAction, boolean isSendRewardInfo) {
        if(multiple <= 0) {
            multiple = 1;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemMap, multiple);
        Map<Integer, Long> changeMap = getFinalChanges(stash);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }else if (isSendRewardInfo) {
            RewardInfoManager.getInstance().resRewardInfoByItemMap(role, logAction.getCode(), changeMap);
        }
        return true;
    }

    public static boolean increase(Role role, List<int[]> items, LogAction logAction) {
        return increase(role, items, logAction, true, false);
    }

    /** 自动使用类道具会变更实际得到 */
    private static Map<Integer, Long> getFinalChanges(BackpackStash stash) {
        Map<Integer, Long> map = new HashMap<>();
        stash.getStash().values().forEach(p-> {
            p.forEach(item-> {
                map.merge(item.getCfgId(), item.getCount(), Long::sum);
            });
        });
        return map;
    }

    public static boolean increase(Role role, Map<Integer, Long> items, LogAction logAction, boolean isSendRewardInfo) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        Map<Integer, Long> changeMap = getFinalChanges(stash);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else if (isSendRewardInfo) {
            RewardInfoManager.getInstance().resRewardInfoByItemMap(role, logAction.getCode(), changeMap);
        }
        return true;
    }

    public static boolean increase(Role role, List<int[]> items, LogAction logAction, boolean isSendRewardInfo) {
        return increase(role, items, logAction, isSendRewardInfo, false);
    }

    public static boolean increase(Role role, List<int[]> items, LogAction logAction, boolean isSendRewardInfo, boolean isSeparate) {
        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        Map<Integer, Long> changeMap = getFinalChanges(stash);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else if (isSendRewardInfo) {
            if (isSeparate) {
                RewardInfoManager.getInstance().resRewardInfo(role, logAction.getCode(), items);
            } else {
                RewardInfoManager.getInstance().resRewardInfoByItemMap(role, logAction.getCode(), changeMap);
            }
        }
        return true;
    }

    public static boolean increase(Role role, List<int[]> items, double multiple, LogAction logAction) {
        return increase(role, items, multiple, logAction, true);
    }

    public static boolean increase(Role role, List<int[]> items, double multiple, LogAction logAction, boolean isSendRewardInfo) {
        BackpackStash stash = new BackpackStash(role);
        List<int[]> trueItem = new ArrayList<>(items.size());
        for (int[] ints : items) {
            if (ints.length < 2) {
                continue;
            }
            long cn = (long) (ints[1] * multiple);
            if (cn < 0) {
                continue;
            }
            trueItem.add(new int[]{ints[0], (int) cn});
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, ints[0], (int) cn);
        }
        stash.increase(trueItem);
        Map<Integer, Long> changeMap = getFinalChanges(stash);
        if (!stash.commit(role, logAction)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        } else if (isSendRewardInfo) {
            RewardInfoManager.getInstance().resRewardInfoByItemMap(role, logAction.getCode(), changeMap);
        }

        return true;
    }

    /**
     * 检查是否可以扣除指定道具
     * @param role 角色
     * @param itemId 道具ID
     * @param count 数量
     * @return 是否可以扣除
     */
    public static boolean canDecrease(Role role, int itemId, long count) {
        Map<Integer, Long> itemMap = new HashMap<>();
        itemMap.put(itemId, count);
        return role.getBackpack().canDecrease(itemMap);
    }

}
