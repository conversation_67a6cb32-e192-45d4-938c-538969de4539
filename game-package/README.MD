# 关斗游戏服务器项目

## 项目概述
这是一个基于Java的游戏服务器项目，采用模块化架构设计，支持多种游戏功能模块。

## 模块结构

### package模块
game-package 打包专属项目

-game-package
 - game-package-core
 - game-package-libs
 - build(目录)

### 核心模块
- **game-server**: 游戏主服务器模块
- **game-script**: 游戏脚本模块，包含各种游戏功能的业务逻辑
- **game-common**: 公共模块，包含通用工具类和常量定义
- **game-protos**: 协议定义模块
- **game-basic**: 基础模块

## 目录结构

- release
  - ddl
  - bin
  - deployment
     - core
     - libs
     - data
     - game
     - conf

- release
  - game
     - core
     - libs
     - data
  - scene
     - core
     - libs
     - data
  - center
  - api(http)

## 最近更新

### 2024-12-19 仙法系统重构
**问题描述**:
`XianFaScript.reqRandomBattleLineup` 方法存在事务一致性问题，在验证阶段就开始修改内存状态，导致验证失败时数据不一致。

**解决方案**:
1. 重构方法为两阶段模式：
   - **第一阶段**: 所有验证逻辑，不修改任何内存状态
   - **第二阶段**: 验证通过后，统一修改内存状态

2. 具体改进：
   - 将仙友次数验证和扣减分离
   - 将道具消耗验证和实际扣除分离
   - 将对手匹配验证和状态设置分离
   - 确保所有验证通过后才开始修改数据

3. 代码健壮性提升：
   - 避免了验证失败时的数据不一致问题
   - 提高了方法的事务性和可靠性
   - 增加了详细的注释说明各阶段的作用

**影响范围**:
- 文件: `game-script/src/main/java/com/sh/game/script/xianfa/XianFaScript.java`
- 方法: `reqRandomBattleLineup`
- 功能: 仙法随机战斗阵容请求

### 2024-12-19 仙法匹配逻辑优化
**问题描述**:
在仙法匹配系统中，当匹配到没有仙友的目标玩家时，系统会直接返回错误，导致匹配失败。

**解决方案**:
1. 实现循环重试机制：
   - 最多尝试5次匹配有效对手
   - 如果匹配到没有仙友的玩家，自动从池子中删除该玩家
   - 继续尝试匹配下一个对手

2. 具体改进：
   - 添加循环逻辑，最多尝试5次
   - 对于机器人对手，直接使用（机器人总是有效的）
   - 对于玩家对手，检查是否有仙友
   - 无仙友的玩家会被从战斗池中移除，避免重复匹配
   - 增加详细的日志记录，便于问题追踪

3. 代码健壮性提升：
   - 避免了因个别无效玩家导致的匹配失败
   - 自动清理无效的战斗池数据
   - 提高了匹配成功率和用户体验

**影响范围**:
- 文件: `game-script/src/main/java/com/sh/game/script/xianfa/XianFaScript.java`
- 方法: `reqRandomBattleLineup` (第122-168行)
- 功能: 仙法随机对手匹配逻辑

## jenkins 参数说明